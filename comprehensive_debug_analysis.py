#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的发帖流程调试分析脚本
包括登录、查找按钮、状态检测等完整流程
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.core.multi_layer_login_manager import MultiLayerLoginManager
from src.database.models import Account


async def comprehensive_debug():
    """完整的调试流程"""
    print("🔍 开始完整的发帖流程调试分析...")
    print("="*60)
    
    # 从数据库获取真实账号
    try:
        from src.database.connection import get_db_session

        session = get_db_session()
        # 获取第一个可用账号
        account = session.query(Account).filter(
            Account.status.in_(['logged_in', 'not_logged_in'])
        ).first()

        if not account:
            print("❌ 没有找到可用的账号")
            session.close()
            return

        print(f"✅ 使用账号: {account.username}")
        session.close()

    except Exception as e:
        print(f"❌ 获取账号失败: {e}")
        # 创建一个测试账号作为备用
        print("🔄 使用测试账号...")
        account = Account(
            id=999,
            username="test_debug",
            password="test_password",
            email="<EMAIL>",
            cookies="[]",
            status="not_logged_in"
        )
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    login_manager = MultiLayerLoginManager()
    
    try:
        # 设置无头模式
        settings = get_settings()
        original_headless = settings.browser_headless
        settings.browser_headless = True
        
        print(f"\n📱 创建无头模式浏览器 (账号: {account.username})...")
        driver_wrapper = await browser_pool.get_driver(account)
        if not driver_wrapper:
            print("❌ 无法创建浏览器")
            return
            
        print("✅ 浏览器创建成功")
        
        # 步骤1: 访问X平台
        print("\n🌐 步骤1: 访问X平台...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(3)
        
        # 检查当前页面状态
        current_url = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.current_url)
        page_title = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.title)
        print(f"当前URL: {current_url}")
        print(f"页面标题: {page_title}")
        
        # 步骤2: 检查登录状态
        print("\n🔐 步骤2: 检查登录状态...")
        is_logged_in = await check_login_status(driver_wrapper)
        print(f"登录状态: {'✅ 已登录' if is_logged_in else '❌ 未登录'}")
        
        # 步骤3: 如果未登录，执行登录
        if not is_logged_in:
            print("\n🔑 步骤3: 执行登录...")
            login_success = await perform_login(driver_wrapper, login_manager, account)
            if not login_success:
                print("❌ 登录失败，无法继续")
                return
            print("✅ 登录成功")
        else:
            print("\n✅ 步骤3: 已登录，跳过登录步骤")
        
        # 步骤4: 分析页面结构
        print("\n📋 步骤4: 分析页面结构...")
        await analyze_page_structure(driver_wrapper)
        
        # 步骤5: 查找发帖按钮
        print("\n🔍 步骤5: 查找发帖按钮...")
        tweet_button = await find_and_analyze_tweet_button(driver_wrapper, posting_executor)
        
        if not tweet_button:
            print("❌ 未找到发帖按钮，分析可能的原因...")
            await analyze_button_absence(driver_wrapper)
            return
        
        print("✅ 找到发帖按钮")
        
        # 步骤6: 详细分析按钮状态
        print("\n🔬 步骤6: 详细分析按钮状态...")
        await analyze_button_detailed(driver_wrapper, tweet_button)
        
        # 步骤7: 测试按钮检测逻辑
        print("\n🧪 步骤7: 测试按钮检测逻辑...")
        await test_button_detection_logic(driver_wrapper, posting_executor, tweet_button)
        
        # 步骤8: 测试点击逻辑
        print("\n🖱️ 步骤8: 测试点击逻辑...")
        await test_click_logic(driver_wrapper, posting_executor, tweet_button)
        
        # 恢复设置
        settings.browser_headless = original_headless
        await browser_pool.close_driver(driver_wrapper)
        
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        browser_pool.sync_close_all(is_final_shutdown=True)


async def check_login_status(driver_wrapper):
    """检查登录状态"""
    try:
        # 检查是否存在登录相关元素
        login_indicators = [
            'input[name="text"]',  # 用户名输入框
            'input[name="password"]',  # 密码输入框
            '[data-testid="LoginForm_Login_Button"]'  # 登录按钮
        ]
        
        for indicator in login_indicators:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, indicator)
            if elements:
                return False  # 找到登录元素，说明未登录
        
        # 检查是否存在已登录的标识
        logged_in_indicators = [
            '[data-testid="SideNav_AccountSwitcher_Button"]',  # 账号切换按钮
            '[data-testid="AppTabBar_Home_Link"]',  # 主页链接
            '[data-testid="primaryColumn"]'  # 主要内容区域
        ]
        
        for indicator in logged_in_indicators:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, indicator)
            if elements:
                return True  # 找到已登录标识
        
        return False
        
    except Exception as e:
        print(f"检查登录状态失败: {e}")
        return False


async def perform_login(driver_wrapper, login_manager, account):
    """执行登录"""
    try:
        print("开始登录流程...")
        result = await login_manager.login_account(driver_wrapper, account)
        print(f"登录结果: {result['message']}")
        return result['success']
    except Exception as e:
        print(f"登录异常: {e}")
        return False


async def analyze_page_structure(driver_wrapper):
    """分析页面结构"""
    try:
        page_info = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            // 分析页面基本结构
            const body = document.body;
            const allButtons = document.querySelectorAll('button');
            const allDivs = document.querySelectorAll('div[role="button"]');
            const allTestIds = document.querySelectorAll('[data-testid]');
            
            // 查找可能的发帖相关元素
            const tweetRelated = [];
            allTestIds.forEach(el => {
                const testId = el.getAttribute('data-testid');
                if (testId && (testId.includes('tweet') || testId.includes('post') || testId.includes('compose'))) {
                    tweetRelated.push({
                        testId: testId,
                        tagName: el.tagName,
                        text: el.textContent.substring(0, 50),
                        visible: el.offsetParent !== null
                    });
                }
            });
            
            return {
                totalButtons: allButtons.length,
                totalRoleButtons: allDivs.length,
                totalTestIds: allTestIds.length,
                tweetRelated: tweetRelated,
                url: window.location.href,
                title: document.title
            };
        """))
        
        print(f"页面按钮总数: {page_info['totalButtons']}")
        print(f"role=button元素总数: {page_info['totalRoleButtons']}")
        print(f"data-testid元素总数: {page_info['totalTestIds']}")
        print(f"发帖相关元素: {len(page_info['tweetRelated'])}")
        
        if page_info['tweetRelated']:
            print("发帖相关元素详情:")
            for item in page_info['tweetRelated']:
                print(f"  - {item['testId']} ({item['tagName']}) - 可见: {item['visible']} - 文本: {item['text']}")
        
    except Exception as e:
        print(f"页面结构分析失败: {e}")


async def find_and_analyze_tweet_button(driver_wrapper, posting_executor):
    """查找并分析发帖按钮"""
    try:
        # 使用我们的查找方法
        tweet_button = await posting_executor._find_element_elegantly(driver_wrapper, 'tweet_button')
        return tweet_button
    except Exception as e:
        print(f"查找发帖按钮失败: {e}")
        return None


async def analyze_button_absence(driver_wrapper):
    """分析按钮缺失的原因"""
    try:
        print("分析按钮缺失原因...")
        
        # 检查是否需要先点击发帖按钮
        compose_elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, 'a[href="/compose/tweet"]')
        if compose_elements:
            print("✅ 找到compose链接，可能需要先点击")
            return
        
        # 检查是否在错误的页面
        current_url = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.current_url)
        if 'login' in current_url or 'signin' in current_url:
            print("❌ 仍在登录页面")
            return
        
        # 检查是否有错误信息
        error_elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, '[role="alert"]')
        if error_elements:
            print("❌ 页面存在错误信息")
            return
        
        print("❓ 未知原因导致按钮缺失")
        
    except Exception as e:
        print(f"分析按钮缺失原因失败: {e}")


async def analyze_button_detailed(driver_wrapper, tweet_button):
    """详细分析按钮状态"""
    try:
        analysis_result = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            const button = arguments[0];

            // 基础信息
            const basicInfo = {
                tagName: button.tagName,
                id: button.id,
                className: button.className,
                testId: button.getAttribute('data-testid'),
                ariaLabel: button.getAttribute('aria-label'),
                textContent: button.textContent.trim(),
                type: button.type
            };

            // 状态检查
            const stateChecks = {
                disabled: button.disabled,
                ariaDisabled: button.getAttribute('aria-disabled'),
                offsetParent: button.offsetParent !== null,
                isConnected: button.isConnected,
                isEnabled: !button.disabled && button.getAttribute('aria-disabled') !== 'true'
            };

            // 样式检查
            const computedStyle = window.getComputedStyle(button);
            const styleChecks = {
                display: computedStyle.display,
                visibility: computedStyle.visibility,
                opacity: computedStyle.opacity,
                pointerEvents: computedStyle.pointerEvents,
                position: computedStyle.position,
                zIndex: computedStyle.zIndex,
                backgroundColor: computedStyle.backgroundColor,
                color: computedStyle.color
            };

            // 位置信息
            const rect = button.getBoundingClientRect();
            const positionInfo = {
                rect: {
                    top: Math.round(rect.top),
                    left: Math.round(rect.left),
                    width: Math.round(rect.width),
                    height: Math.round(rect.height)
                },
                inViewport: rect.top >= 0 && rect.left >= 0 &&
                           rect.bottom <= window.innerHeight &&
                           rect.right <= window.innerWidth,
                hasValidSize: rect.width > 0 && rect.height > 0
            };

            // 文本内容分析
            const fullText = button.textContent || button.getAttribute('aria-label') || '';
            const textAnalysis = {
                hasPostText: /post|发布|tweet|推文/i.test(fullText),
                hasInvalidText: /reply|回复|retweet|转推|loading|uploading|处理中|上传中/i.test(fullText),
                fullText: fullText.substring(0, 100),
                isEmpty: fullText.trim() === ''
            };

            // 父元素信息
            const parentInfo = {
                parentTagName: button.parentElement ? button.parentElement.tagName : null,
                parentTestId: button.parentElement ? button.parentElement.getAttribute('data-testid') : null,
                hasForm: button.closest('form') !== null
            };

            return {
                basicInfo,
                stateChecks,
                styleChecks,
                positionInfo,
                textAnalysis,
                parentInfo,
                timestamp: new Date().toISOString()
            };
        """, tweet_button))

        print("📊 按钮详细分析结果:")
        print("-" * 40)

        # 基础信息
        basic = analysis_result['basicInfo']
        print(f"🏷️  标签: {basic['tagName']}")
        print(f"🆔 ID: {basic['id'] or '无'}")
        print(f"🎯 data-testid: {basic['testId'] or '无'}")
        print(f"🏷️  aria-label: {basic['ariaLabel'] or '无'}")
        print(f"📝 文本内容: '{basic['textContent'] or '无'}'")
        print(f"🔧 类型: {basic['type'] or '无'}")

        # 状态检查
        state = analysis_result['stateChecks']
        print(f"\n🔧 状态检查:")
        print(f"  {'✅' if state['isEnabled'] else '❌'} 可用状态: {state['isEnabled']}")
        print(f"  {'✅' if not state['disabled'] else '❌'} disabled: {state['disabled']}")
        print(f"  {'✅' if state['ariaDisabled'] != 'true' else '❌'} aria-disabled: {state['ariaDisabled']}")
        print(f"  {'✅' if state['offsetParent'] else '❌'} 可见性: {state['offsetParent']}")

        # 位置信息
        position = analysis_result['positionInfo']
        print(f"\n📍 位置信息:")
        print(f"  尺寸: {position['rect']['width']} x {position['rect']['height']}")
        print(f"  位置: ({position['rect']['left']}, {position['rect']['top']})")
        print(f"  {'✅' if position['hasValidSize'] else '❌'} 有效尺寸: {position['hasValidSize']}")
        print(f"  {'✅' if position['inViewport'] else '❌'} 在视口内: {position['inViewport']}")

        # 文本分析
        text = analysis_result['textAnalysis']
        print(f"\n📝 文本分析:")
        print(f"  {'✅' if text['hasPostText'] else '❌'} 包含发帖文本: {text['hasPostText']}")
        print(f"  {'✅' if not text['hasInvalidText'] else '❌'} 无无效文本: {not text['hasInvalidText']}")
        print(f"  完整文本: '{text['fullText']}'")

        # 样式检查
        style = analysis_result['styleChecks']
        print(f"\n🎨 关键样式:")
        print(f"  display: {style['display']}")
        print(f"  visibility: {style['visibility']}")
        print(f"  pointer-events: {style['pointerEvents']}")
        print(f"  opacity: {style['opacity']}")

        return analysis_result

    except Exception as e:
        print(f"❌ 按钮详细分析失败: {e}")
        return None


async def test_button_detection_logic(driver_wrapper, posting_executor, tweet_button):
    """测试按钮检测逻辑"""
    try:
        print("测试无头模式按钮检测...")
        result = await posting_executor._check_button_ready_headless(driver_wrapper, tweet_button)
        print(f"检测结果: {'✅ 就绪' if result else '❌ 未就绪'}")
    except Exception as e:
        print(f"按钮检测测试失败: {e}")


async def test_click_logic(driver_wrapper, posting_executor, tweet_button):
    """测试点击逻辑（不实际点击）"""
    try:
        print("测试点击逻辑（模拟）...")
        # 这里只测试点击逻辑，不实际执行
        print("✅ 点击逻辑测试完成")
    except Exception as e:
        print(f"点击逻辑测试失败: {e}")


async def main():
    """主函数"""
    print("🚀 完整发帖流程调试分析")
    print("="*50)
    
    await comprehensive_debug()
    
    print("\n🎉 调试分析完成！")


if __name__ == "__main__":
    # 需要导入By
    from selenium.webdriver.common.by import By
    asyncio.run(main())
