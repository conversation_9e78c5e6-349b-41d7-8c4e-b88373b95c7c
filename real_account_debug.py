#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实账号14进行完整的发帖流程调试
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.core.multi_layer_login_manager import MultiLayerLoginManager
from src.core.account_manager import AccountManager
from src.database.models import Account
from selenium.webdriver.common.by import By


async def debug_with_real_account():
    """使用真实账号进行调试"""
    print("🔍 使用真实账号14进行完整发帖流程调试")
    print("="*60)
    
    # 初始化数据库
    try:
        print("📊 初始化数据库...")
        settings = get_settings()
        from src.database.connection import init_db_manager
        init_db_manager(settings.database_url)
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return
    
    # 获取真实账号
    try:
        print("🔍 获取账号14...")
        account_manager = AccountManager()
        account = account_manager.get_account_by_id(14)
        
        if not account:
            print("❌ 未找到账号14")
            # 尝试获取任何可用账号
            accounts, total = account_manager.get_accounts()
            if accounts:
                account = accounts[0]
                print(f"✅ 使用账号: {account.username} (ID: {account.id})")
            else:
                print("❌ 没有找到任何账号")
                return
        else:
            print(f"✅ 找到账号14: {account.username}")
            
    except Exception as e:
        print(f"❌ 获取账号失败: {e}")
        return
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    login_manager = MultiLayerLoginManager()
    
    try:
        # 设置无头模式
        original_headless = settings.browser_headless
        settings.browser_headless = True
        
        print(f"\n📱 创建无头模式浏览器 (账号: {account.username})...")
        driver_wrapper = await browser_pool.get_driver(account)
        if not driver_wrapper:
            print("❌ 无法创建浏览器")
            return
            
        print("✅ 浏览器创建成功")
        
        # 步骤1: 访问X平台
        print("\n🌐 步骤1: 访问X平台...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(3)
        
        current_url = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.current_url)
        page_title = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.title)
        print(f"当前URL: {current_url}")
        print(f"页面标题: {page_title}")
        
        # 步骤2: 执行登录
        print("\n🔑 步骤2: 执行登录...")
        login_result = await login_manager.login_account(driver_wrapper, account)
        print(f"登录结果: {login_result['message']}")
        
        if not login_result['success']:
            print("❌ 登录失败，但继续分析页面结构...")
        else:
            print("✅ 登录成功")
            # 登录成功后等待页面稳定
            await asyncio.sleep(5)
        
        # 步骤3: 分析当前页面状态
        print("\n📋 步骤3: 分析页面状态...")
        await analyze_current_page(driver_wrapper)
        
        # 步骤4: 尝试访问compose页面
        print("\n📝 步骤4: 访问compose页面...")
        await driver_wrapper.get("https://x.com/compose/tweet")
        await asyncio.sleep(3)
        
        current_url = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.current_url)
        print(f"Compose页面URL: {current_url}")
        
        # 步骤5: 分析compose页面的按钮
        print("\n🔍 步骤5: 分析compose页面按钮...")
        await analyze_compose_page_buttons(driver_wrapper, posting_executor)
        
        # 步骤6: 测试完整的发帖流程
        print("\n🚀 步骤6: 测试完整发帖流程...")
        await test_complete_posting_flow(driver_wrapper, posting_executor, account)
        
        # 恢复设置
        settings.browser_headless = original_headless
        await browser_pool.close_driver(driver_wrapper)
        
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        browser_pool.sync_close_all(is_final_shutdown=True)


async def analyze_current_page(driver_wrapper):
    """分析当前页面状态"""
    try:
        page_info = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            // 检查登录状态指示器
            const loginIndicators = {
                hasLoginForm: document.querySelector('input[name="text"]') !== null,
                hasPasswordField: document.querySelector('input[name="password"]') !== null,
                hasLoginButton: document.querySelector('[data-testid="LoginForm_Login_Button"]') !== null,
                hasHomeTimeline: document.querySelector('[data-testid="primaryColumn"]') !== null,
                hasAccountSwitcher: document.querySelector('[data-testid="SideNav_AccountSwitcher_Button"]') !== null,
                hasComposeButton: document.querySelector('a[href="/compose/tweet"]') !== null
            };
            
            // 查找所有可能的发帖相关元素
            const tweetElements = [];
            const allTestIds = document.querySelectorAll('[data-testid]');
            allTestIds.forEach(el => {
                const testId = el.getAttribute('data-testid');
                if (testId && (testId.includes('tweet') || testId.includes('post') || testId.includes('compose'))) {
                    tweetElements.push({
                        testId: testId,
                        tagName: el.tagName,
                        text: el.textContent.substring(0, 50),
                        visible: el.offsetParent !== null,
                        href: el.getAttribute('href') || ''
                    });
                }
            });
            
            return {
                url: window.location.href,
                title: document.title,
                loginIndicators: loginIndicators,
                tweetElements: tweetElements
            };
        """))
        
        print(f"页面URL: {page_info['url']}")
        print(f"页面标题: {page_info['title']}")
        
        # 分析登录状态
        indicators = page_info['loginIndicators']
        print(f"\n🔐 登录状态分析:")
        print(f"  {'❌' if indicators['hasLoginForm'] else '✅'} 登录表单: {indicators['hasLoginForm']}")
        print(f"  {'✅' if indicators['hasHomeTimeline'] else '❌'} 主页时间线: {indicators['hasHomeTimeline']}")
        print(f"  {'✅' if indicators['hasAccountSwitcher'] else '❌'} 账号切换器: {indicators['hasAccountSwitcher']}")
        print(f"  {'✅' if indicators['hasComposeButton'] else '❌'} 发帖按钮: {indicators['hasComposeButton']}")
        
        # 显示发帖相关元素
        if page_info['tweetElements']:
            print(f"\n🎯 发帖相关元素 ({len(page_info['tweetElements'])}):")
            for item in page_info['tweetElements']:
                status = "✅" if item['visible'] else "❌"
                print(f"  {status} {item['testId']} ({item['tagName']}) - {item['text']}")
                if item['href']:
                    print(f"      href: {item['href']}")
        
        # 判断是否已登录
        is_logged_in = (indicators['hasHomeTimeline'] or indicators['hasAccountSwitcher']) and not indicators['hasLoginForm']
        print(f"\n📊 登录状态判断: {'✅ 已登录' if is_logged_in else '❌ 未登录'}")
        
        return is_logged_in
        
    except Exception as e:
        print(f"❌ 页面分析失败: {e}")
        return False


async def analyze_compose_page_buttons(driver_wrapper, posting_executor):
    """分析compose页面的按钮"""
    try:
        print("🔍 分析compose页面按钮...")
        
        # 查找所有可能的发帖按钮
        button_analysis = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            const buttons = [];
            
            // 查找所有button元素
            document.querySelectorAll('button').forEach((btn, index) => {
                const rect = btn.getBoundingClientRect();
                const testId = btn.getAttribute('data-testid') || '';
                const ariaLabel = btn.getAttribute('aria-label') || '';
                const text = btn.textContent.trim();
                
                // 只关注可能的发帖按钮
                if (testId.includes('tweet') || testId.includes('post') || 
                    ariaLabel.toLowerCase().includes('post') || ariaLabel.toLowerCase().includes('tweet') ||
                    text.toLowerCase().includes('post') || text.toLowerCase().includes('tweet')) {
                    
                    buttons.push({
                        index: index,
                        testId: testId,
                        ariaLabel: ariaLabel,
                        text: text,
                        disabled: btn.disabled,
                        ariaDisabled: btn.getAttribute('aria-disabled'),
                        visible: btn.offsetParent !== null,
                        rect: {
                            width: Math.round(rect.width),
                            height: Math.round(rect.height),
                            top: Math.round(rect.top),
                            left: Math.round(rect.left)
                        },
                        style: {
                            display: window.getComputedStyle(btn).display,
                            visibility: window.getComputedStyle(btn).visibility,
                            opacity: window.getComputedStyle(btn).opacity
                        }
                    });
                }
            });
            
            return buttons;
        """))
        
        if button_analysis:
            print(f"找到 {len(button_analysis)} 个可能的发帖按钮:")
            for i, btn in enumerate(button_analysis):
                enabled = not btn['disabled'] and btn['ariaDisabled'] != 'true'
                visible = btn['visible'] and btn['rect']['width'] > 0 and btn['rect']['height'] > 0
                status = "✅" if enabled and visible else "❌"
                
                print(f"  {status} 按钮{i+1}:")
                print(f"      testId: {btn['testId']}")
                print(f"      aria-label: {btn['ariaLabel']}")
                print(f"      文本: '{btn['text']}'")
                print(f"      可用: {enabled}")
                print(f"      可见: {visible}")
                print(f"      尺寸: {btn['rect']['width']}x{btn['rect']['height']}")
                
                # 如果找到了按钮，测试我们的检测逻辑
                if enabled and visible and btn['testId']:
                    print(f"      🧪 测试检测逻辑...")
                    await test_button_detection_on_element(driver_wrapper, posting_executor, btn['testId'])
        else:
            print("❌ 未找到任何发帖按钮")
            
    except Exception as e:
        print(f"❌ compose页面按钮分析失败: {e}")


async def test_button_detection_on_element(driver_wrapper, posting_executor, test_id):
    """测试特定元素的按钮检测"""
    try:
        # 通过testId查找元素
        elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, f'[data-testid="{test_id}"]')
        if elements:
            button = elements[0]
            
            # 测试无头模式检测
            is_ready = await posting_executor._check_button_ready_headless(driver_wrapper, button)
            print(f"        检测结果: {'✅ 就绪' if is_ready else '❌ 未就绪'}")
            
            return is_ready
        else:
            print(f"        ❌ 无法重新找到元素: {test_id}")
            return False
            
    except Exception as e:
        print(f"        ❌ 检测异常: {e}")
        return False


async def test_complete_posting_flow(driver_wrapper, posting_executor, account):
    """测试完整的发帖流程"""
    try:
        print("🚀 测试完整发帖流程...")
        
        # 准备测试文案
        test_content = "🧪 这是一个测试发帖，用于调试按钮问题"
        
        print(f"📝 测试文案: {test_content}")
        
        # 执行发帖
        success, message, post_url = await posting_executor.execute_post(
            account=account,
            content=test_content,
            media_paths=[]
        )
        
        print(f"📊 发帖结果:")
        print(f"  成功: {'✅' if success else '❌'}")
        print(f"  消息: {message}")
        if post_url:
            print(f"  链接: {post_url}")
            
        return success
        
    except Exception as e:
        print(f"❌ 完整发帖流程测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 真实账号发帖调试工具")
    print("="*50)
    
    await debug_with_real_account()
    
    print("\n🎉 调试完成！")


if __name__ == "__main__":
    asyncio.run(main())
