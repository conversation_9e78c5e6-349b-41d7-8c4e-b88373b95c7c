#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析发帖按钮状态的调试脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.database.models import Account


async def analyze_button_state():
    """深度分析按钮状态"""
    print("🔍 开始深度分析发帖按钮状态...")
    
    # 创建测试账号
    test_account = Account(
        id=999,
        username="debug_test",
        password="test_password",
        email="<EMAIL>", 
        cookies="[]",
        status="logged_in"
    )
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    
    try:
        # 设置无头模式
        settings = get_settings()
        settings.browser_headless = True
        
        print("📱 创建无头模式浏览器...")
        driver_wrapper = await browser_pool.get_driver(test_account)
        if not driver_wrapper:
            print("❌ 无法创建浏览器")
            return
            
        # 访问X平台
        print("🌐 访问X平台...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(5)
        
        # 查找发帖按钮
        print("🔍 查找发帖按钮...")
        tweet_button = await posting_executor._find_element_elegantly(driver_wrapper, 'tweet_button')
        
        if not tweet_button:
            print("❌ 未找到发帖按钮")
            return
            
        print("✅ 找到发帖按钮，开始详细分析...")
        
        # 执行详细的按钮分析
        analysis_result = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            const button = arguments[0];
            
            // 基础信息
            const basicInfo = {
                tagName: button.tagName,
                id: button.id,
                className: button.className,
                testId: button.getAttribute('data-testid'),
                ariaLabel: button.getAttribute('aria-label'),
                textContent: button.textContent,
                innerHTML: button.innerHTML.substring(0, 200) + '...'
            };
            
            // 状态检查
            const stateChecks = {
                disabled: button.disabled,
                ariaDisabled: button.getAttribute('aria-disabled'),
                offsetParent: button.offsetParent !== null,
                isConnected: button.isConnected
            };
            
            // 样式检查
            const computedStyle = window.getComputedStyle(button);
            const styleChecks = {
                display: computedStyle.display,
                visibility: computedStyle.visibility,
                opacity: computedStyle.opacity,
                pointerEvents: computedStyle.pointerEvents,
                position: computedStyle.position,
                zIndex: computedStyle.zIndex
            };
            
            // 位置信息
            const rect = button.getBoundingClientRect();
            const positionInfo = {
                rect: {
                    top: rect.top,
                    left: rect.left,
                    width: rect.width,
                    height: rect.height,
                    bottom: rect.bottom,
                    right: rect.right
                },
                inViewport: rect.top >= 0 && rect.left >= 0 && 
                           rect.bottom <= window.innerHeight && 
                           rect.right <= window.innerWidth
            };
            
            // 父元素信息
            const parentInfo = {
                parentTagName: button.parentElement ? button.parentElement.tagName : null,
                parentClassName: button.parentElement ? button.parentElement.className : null,
                parentTestId: button.parentElement ? button.parentElement.getAttribute('data-testid') : null
            };
            
            // 事件监听器检查（简化版）
            const eventInfo = {
                hasClickListener: button.onclick !== null,
                hasEventListeners: button.addEventListener !== undefined
            };
            
            // 文本内容分析
            const textAnalysis = {
                hasPostText: /post|发布|tweet|推文/i.test(button.textContent || button.getAttribute('aria-label') || ''),
                hasInvalidText: /reply|回复|retweet|转推|loading|uploading|处理中|上传中/i.test(button.textContent || button.getAttribute('aria-label') || ''),
                fullText: (button.textContent || button.getAttribute('aria-label') || '').substring(0, 100)
            };
            
            return {
                basicInfo,
                stateChecks,
                styleChecks,
                positionInfo,
                parentInfo,
                eventInfo,
                textAnalysis,
                timestamp: new Date().toISOString()
            };
        """, tweet_button))
        
        print("\n📊 按钮分析结果:")
        print("="*60)
        
        print("\n🏷️ 基础信息:")
        basic = analysis_result['basicInfo']
        for key, value in basic.items():
            print(f"  {key}: {value}")
            
        print("\n🔧 状态检查:")
        state = analysis_result['stateChecks']
        for key, value in state.items():
            status = "✅" if value else "❌"
            print(f"  {status} {key}: {value}")
            
        print("\n🎨 样式检查:")
        style = analysis_result['styleChecks']
        for key, value in style.items():
            print(f"  {key}: {value}")
            
        print("\n📍 位置信息:")
        position = analysis_result['positionInfo']
        print(f"  位置: ({position['rect']['left']}, {position['rect']['top']})")
        print(f"  尺寸: {position['rect']['width']} x {position['rect']['height']}")
        print(f"  在视口内: {'✅' if position['inViewport'] else '❌'}")
        
        print("\n👨‍👩‍👧‍👦 父元素信息:")
        parent = analysis_result['parentInfo']
        for key, value in parent.items():
            print(f"  {key}: {value}")
            
        print("\n📝 文本分析:")
        text = analysis_result['textAnalysis']
        for key, value in text.items():
            if key == 'hasPostText' or key == 'hasInvalidText':
                status = "✅" if (key == 'hasPostText' and value) or (key == 'hasInvalidText' and not value) else "❌"
                print(f"  {status} {key}: {value}")
            else:
                print(f"  {key}: {value}")
        
        # 执行我们的检测逻辑
        print("\n🧪 执行我们的检测逻辑:")
        button_ready = await posting_executor._check_button_ready_headless(driver_wrapper, tweet_button)
        print(f"检测结果: {'✅ 就绪' if button_ready else '❌ 未就绪'}")
        
        await browser_pool.close_driver(driver_wrapper)

    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # BrowserPool没有cleanup方法，使用sync_close_all
        browser_pool.sync_close_all(is_final_shutdown=True)


async def main():
    """主函数"""
    print("🔍 发帖按钮深度分析工具")
    print("="*50)
    
    await analyze_button_state()
    
    print("\n🎉 分析完成！")


if __name__ == "__main__":
    asyncio.run(main())
