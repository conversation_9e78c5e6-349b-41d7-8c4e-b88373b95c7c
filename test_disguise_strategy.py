#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试巧妙伪装策略的效果
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.core.multi_layer_login_manager import MultiLayerLoginManager
from src.core.account_manager import AccountManager
from src.database.models import Account


async def test_disguise_strategy():
    """测试伪装策略"""
    print("🎭 测试巧妙伪装策略")
    print("="*50)
    
    # 初始化数据库
    try:
        print("📊 初始化数据库...")
        settings = get_settings()
        from src.database.connection import init_db_manager
        init_db_manager(settings.database_url)
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return
    
    # 获取真实账号
    try:
        print("🔍 获取账号14...")
        account_manager = AccountManager()
        account = account_manager.get_account_by_id(14)
        
        if not account:
            accounts, total = account_manager.get_accounts()
            if accounts:
                account = accounts[0]
                print(f"✅ 使用账号: {account.username} (ID: {account.id})")
            else:
                print("❌ 没有找到任何账号")
                return
        else:
            print(f"✅ 找到账号14: {account.username}")
            
    except Exception as e:
        print(f"❌ 获取账号失败: {e}")
        return
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    login_manager = MultiLayerLoginManager()
    
    try:
        # 设置无头模式
        original_headless = settings.browser_headless
        settings.browser_headless = True
        
        print(f"\n📱 创建无头模式浏览器...")
        driver_wrapper = await browser_pool.get_driver(account)
        if not driver_wrapper:
            print("❌ 无法创建浏览器")
            return
            
        print("✅ 浏览器创建成功")
        
        # 步骤1: 登录
        print("\n🔑 步骤1: 执行登录...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(3)
        
        login_result = await login_manager.login_account(driver_wrapper, account)
        if not login_result['success']:
            print(f"❌ 登录失败: {login_result['message']}")
            return
        
        print("✅ 登录成功")
        await asyncio.sleep(3)
        
        # 步骤2: 访问compose页面
        print("\n📝 步骤2: 访问compose页面...")
        await driver_wrapper.get("https://x.com/compose/post")
        await asyncio.sleep(5)
        
        # 步骤3: 测试伪装策略
        print("\n🎭 步骤3: 测试伪装策略...")
        test_content = "🎭 测试巧妙伪装策略 - 绕过反自动化检测"
        
        # 查找按钮
        tweet_button = await posting_executor._find_element_elegantly(driver_wrapper, 'tweet_button')
        if not tweet_button:
            print("❌ 未找到发帖按钮")
            return
        
        print("✅ 找到发帖按钮")
        
        # 测试伪装策略
        print("\n🎭 执行巧妙伪装策略...")
        activation_success = await posting_executor._smart_button_activation(
            driver_wrapper, tweet_button, test_content, []
        )
        
        if activation_success:
            print("✅ 伪装策略成功！按钮已激活")
            
            # 测试点击
            print("\n🖱️ 测试按钮点击...")
            if settings.browser_headless:
                click_success = await posting_executor._click_button_headless(driver_wrapper, tweet_button)
            else:
                click_success = await posting_executor._elegant_click(driver_wrapper, tweet_button)
            
            if click_success:
                print("✅ 按钮点击成功！")
                print("🎉 伪装策略完全成功！")
            else:
                print("❌ 按钮点击失败")
        else:
            print("❌ 伪装策略失败")
        
        # 步骤4: 测试完整发帖流程（如果用户同意）
        print("\n🚀 步骤4: 是否测试完整发帖流程？")
        print("⚠️ 注意：这将实际发布一条测试推文")
        
        # 这里我们跳过实际发帖，只是展示流程
        print("🔄 跳过实际发帖，避免垃圾内容")
        
        # 恢复设置
        settings.browser_headless = original_headless
        browser_pool.sync_close_all(is_final_shutdown=True)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            browser_pool.sync_close_all(is_final_shutdown=True)
        except:
            pass


def show_strategy_summary():
    """显示策略总结"""
    print("\n" + "="*60)
    print("🎭 巧妙伪装策略总结")
    print("="*60)
    
    print("\n🎯 核心思路:")
    print("  ✅ 不与X平台对抗，而是巧妙伪装")
    print("  ✅ 利用图片上传机制激活按钮")
    print("  ✅ 模拟真实用户交互序列")
    print("  ✅ 触发必要的DOM事件")
    
    print("\n🛠️ 伪装策略:")
    print("  1️⃣ 常规检测 - 先尝试正常流程")
    print("  2️⃣ 状态模拟 - 模拟图片上传状态变化")
    print("  3️⃣ 图片利用 - 如有图片，利用上传机制")
    print("  4️⃣ 深度伪装 - 模拟完整用户交互")
    
    print("\n🎭 伪装技巧:")
    print("  🔄 重新聚焦文本框")
    print("  ⌨️ 模拟逐字输入过程")
    print("  🎯 触发所有必要事件")
    print("  ⏱️ 合理的时间间隔")
    print("  🖱️ 真实的鼠标交互")
    
    print("\n📊 预期效果:")
    print("  🎯 绕过反自动化检测")
    print("  🎯 按钮正常激活")
    print("  🎯 文案正确显示")
    print("  🎯 降低封号风险")
    
    print("\n💡 关键优势:")
    print("  ✅ 不强制修改DOM")
    print("  ✅ 模拟真实用户行为")
    print("  ✅ 利用平台自身机制")
    print("  ✅ 保持隐蔽性")


async def main():
    """主函数"""
    print("🚀 巧妙伪装策略测试工具")
    print("="*50)
    
    await test_disguise_strategy()
    
    show_strategy_summary()
    
    print("\n🎉 测试完成！")
    print("\n💡 使用建议:")
    print("  1. 这个策略更加隐蔽和安全")
    print("  2. 模拟真实用户行为，降低检测风险")
    print("  3. 如果有图片，优先使用图片上传机制")
    print("  4. 保持合理的操作间隔")


if __name__ == "__main__":
    asyncio.run(main())
