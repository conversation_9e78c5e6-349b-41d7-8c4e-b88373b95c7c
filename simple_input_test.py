#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的输入测试 - 专注于send_keys策略
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.core.multi_layer_login_manager import MultiLayerLoginManager
from src.core.account_manager import AccountManager
from selenium.webdriver.common.by import By


async def simple_input_test():
    """简单的输入测试"""
    print("⌨️ 简单输入测试 - 专注于send_keys")
    print("="*50)
    
    # 初始化数据库
    try:
        print("📊 初始化数据库...")
        settings = get_settings()
        from src.database.connection import init_db_manager
        init_db_manager(settings.database_url)
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return
    
    # 获取真实账号
    try:
        print("🔍 获取账号13...")
        account_manager = AccountManager()
        account = account_manager.get_account_by_id(13)
        
        if not account:
            accounts, total = account_manager.get_accounts()
            if accounts:
                account = accounts[0]
                print(f"✅ 使用账号: {account.username} (ID: {account.id})")
            else:
                print("❌ 没有找到任何账号")
                return
        else:
            print(f"✅ 找到账号13: {account.username}")
            
    except Exception as e:
        print(f"❌ 获取账号失败: {e}")
        return
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    login_manager = MultiLayerLoginManager()
    
    try:
        # 设置无头模式
        original_headless = settings.browser_headless
        settings.browser_headless = True
        
        print(f"\n📱 创建无头模式浏览器...")
        driver_wrapper = await browser_pool.get_driver(account)
        if not driver_wrapper:
            print("❌ 无法创建浏览器")
            return
            
        print("✅ 浏览器创建成功")
        
        # 步骤1: 登录
        print("\n🔑 步骤1: 执行登录...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(3)
        
        login_result = await login_manager.login_account(driver_wrapper, account)
        if not login_result['success']:
            print(f"❌ 登录失败: {login_result['message']}")
            return
        
        print("✅ 登录成功")
        await asyncio.sleep(3)
        
        # 步骤2: 访问compose页面
        print("\n📝 步骤2: 访问compose页面...")
        await driver_wrapper.get("https://x.com/compose/post")
        await asyncio.sleep(5)
        
        # 步骤3: 简单的send_keys测试
        print("\n⌨️ 步骤3: 简单send_keys测试...")
        test_content = "⌨️ 简单测试 - send_keys输入"
        
        success = await simple_send_keys_input(driver_wrapper, test_content)
        if success:
            print("✅ send_keys输入成功！")
            
            # 测试按钮状态
            print("\n🔍 步骤4: 检查按钮状态...")
            button_ready = await check_button_after_input(driver_wrapper, posting_executor)
            if button_ready:
                print("🎉 按钮已激活！send_keys策略成功！")
            else:
                print("😞 按钮仍未激活")
        else:
            print("❌ send_keys输入失败")
        
        # 恢复设置
        settings.browser_headless = original_headless
        browser_pool.sync_close_all(is_final_shutdown=True)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            browser_pool.sync_close_all(is_final_shutdown=True)
        except:
            pass


async def simple_send_keys_input(driver_wrapper, content: str) -> bool:
    """简单的send_keys输入测试"""
    try:
        print(f"📝 输入内容: '{content}'")
        
        # 查找文本框
        selectors = [
            'div[data-testid="tweetTextarea_0"]',
            '[data-testid="tweetTextarea_0"]',
            'div[contenteditable="true"]'
        ]
        
        textbox = None
        for selector in selectors:
            try:
                elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    textbox = elements[0]
                    print(f"✅ 找到文本框: {selector}")
                    break
            except Exception as e:
                print(f"选择器 {selector} 失败: {e}")
                continue
        
        if not textbox:
            print("❌ 未找到文本框")
            return False
        
        # 点击文本框
        print("🖱️ 点击文本框...")
        await driver_wrapper.execute_async(lambda: textbox.click())
        await asyncio.sleep(0.5)
        
        # 清空内容
        print("🧹 清空现有内容...")
        from selenium.webdriver.common.keys import Keys
        await driver_wrapper.execute_async(lambda: textbox.send_keys(Keys.CONTROL + 'a'))
        await asyncio.sleep(0.2)
        await driver_wrapper.execute_async(lambda: textbox.send_keys(Keys.DELETE))
        await asyncio.sleep(0.3)
        
        # 输入内容
        print("⌨️ 输入新内容...")
        await driver_wrapper.execute_async(lambda: textbox.send_keys(content))
        await asyncio.sleep(1)
        
        # 验证输入
        print("🔍 验证输入结果...")
        current_text = await get_textbox_content(driver_wrapper)
        print(f"当前文本框内容: '{current_text}'")
        
        # 检查内容是否匹配
        if content.strip() in current_text:
            print("✅ 内容输入验证成功")
            return True
        else:
            print("❌ 内容输入验证失败")
            return False
        
    except Exception as e:
        print(f"❌ send_keys输入异常: {e}")
        return False


async def get_textbox_content(driver_wrapper) -> str:
    """获取文本框内容"""
    try:
        selectors = [
            'div[data-testid="tweetTextarea_0"]',
            '[data-testid="tweetTextarea_0"]',
            'div[contenteditable="true"]'
        ]
        
        for selector in selectors:
            try:
                elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    textbox = elements[0]
                    text_content = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
                        const element = arguments[0];
                        return element.textContent || element.innerText || '';
                    """, textbox))
                    return text_content.strip()
            except Exception as e:
                continue
        
        return ""
        
    except Exception as e:
        print(f"获取文本框内容失败: {e}")
        return ""


async def check_button_after_input(driver_wrapper, posting_executor) -> bool:
    """检查输入后的按钮状态"""
    try:
        print("🔍 查找发帖按钮...")
        tweet_button = await posting_executor._find_element_elegantly(driver_wrapper, 'tweet_button')
        if not tweet_button:
            print("❌ 未找到发帖按钮")
            return False
        
        print("✅ 找到发帖按钮")
        
        # 检查按钮状态
        print("🔍 检查按钮状态...")
        is_ready = await posting_executor._check_button_ready_headless(driver_wrapper, tweet_button)
        print(f"按钮状态: {'✅ 就绪' if is_ready else '❌ 未就绪'}")
        
        return is_ready
        
    except Exception as e:
        print(f"❌ 检查按钮状态失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 简单输入测试工具")
    print("="*50)
    
    await simple_input_test()
    
    print("\n📊 测试总结:")
    print("  🎯 专注测试send_keys策略")
    print("  🎯 验证输入是否能激活按钮")
    print("  🎯 寻找有效的输入方式")
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
