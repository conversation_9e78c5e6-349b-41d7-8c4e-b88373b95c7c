#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比测试：无头模式 vs 有头模式
验证是否真的是无头模式检测问题
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.core.multi_layer_login_manager import MultiLayerLoginManager
from src.core.account_manager import AccountManager
from selenium.webdriver.common.by import By


async def compare_headless_vs_headed():
    """对比无头模式和有头模式"""
    print("🔍 无头模式 vs 有头模式对比测试")
    print("="*60)
    
    # 初始化数据库
    try:
        print("📊 初始化数据库...")
        settings = get_settings()
        from src.database.connection import init_db_manager
        init_db_manager(settings.database_url)
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return
    
    # 获取真实账号
    try:
        print("🔍 获取账号13...")
        account_manager = AccountManager()
        account = account_manager.get_account_by_id(13)
        
        if not account:
            accounts, total = account_manager.get_accounts()
            if accounts:
                account = accounts[0]
                print(f"✅ 使用账号: {account.username} (ID: {account.id})")
            else:
                print("❌ 没有找到任何账号")
                return
        else:
            print(f"✅ 找到账号13: {account.username}")
            
    except Exception as e:
        print(f"❌ 获取账号失败: {e}")
        return
    
    # 保存原始设置
    original_headless = settings.browser_headless
    
    try:
        # 测试1: 有头模式
        print(f"\n" + "="*60)
        print("🖥️ 测试1: 有头模式")
        print("="*60)
        
        settings.browser_headless = False
        headed_result = await test_mode(account, "有头模式", False)
        
        # 等待用户确认
        print("\n⏸️ 有头模式测试完成，请查看结果")
        print("按Enter继续无头模式测试...")
        input()
        
        # 测试2: 无头模式
        print(f"\n" + "="*60)
        print("👻 测试2: 无头模式")
        print("="*60)
        
        settings.browser_headless = True
        headless_result = await test_mode(account, "无头模式", True)
        
        # 对比结果
        print(f"\n" + "="*60)
        print("📊 对比结果")
        print("="*60)
        
        print(f"有头模式结果:")
        print(f"  输入成功: {'✅' if headed_result['input_success'] else '❌'}")
        print(f"  按钮激活: {'✅' if headed_result['button_ready'] else '❌'}")
        
        print(f"\n无头模式结果:")
        print(f"  输入成功: {'✅' if headless_result['input_success'] else '❌'}")
        print(f"  按钮激活: {'✅' if headless_result['button_ready'] else '❌'}")
        
        # 分析结论
        if headed_result['button_ready'] and not headless_result['button_ready']:
            print(f"\n🎯 结论: X平台确实检测无头模式并禁用按钮！")
            print(f"💡 解决方案: 需要更深层的反检测技术")
        elif not headed_result['button_ready'] and not headless_result['button_ready']:
            print(f"\n🤔 结论: 两种模式都失败，可能是其他问题")
        elif headless_result['button_ready']:
            print(f"\n🎉 结论: 无头模式也能成功！")
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复设置
        settings.browser_headless = original_headless


async def test_mode(account, mode_name: str, is_headless: bool) -> dict:
    """测试特定模式"""
    result = {
        'input_success': False,
        'button_ready': False
    }
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    login_manager = MultiLayerLoginManager()
    
    try:
        print(f"\n📱 创建{mode_name}浏览器...")
        driver_wrapper = await browser_pool.get_driver(account)
        if not driver_wrapper:
            print(f"❌ 无法创建{mode_name}浏览器")
            return result
            
        print(f"✅ {mode_name}浏览器创建成功")
        
        # 登录
        print(f"\n🔑 {mode_name}登录...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(3)
        
        login_result = await login_manager.login_account(driver_wrapper, account)
        if not login_result['success']:
            print(f"❌ {mode_name}登录失败: {login_result['message']}")
            return result
        
        print(f"✅ {mode_name}登录成功")
        await asyncio.sleep(3)
        
        # 访问compose页面
        print(f"\n📝 {mode_name}访问compose页面...")
        await driver_wrapper.get("https://x.com/compose/post")
        await asyncio.sleep(5)
        
        # 输入测试
        print(f"\n⌨️ {mode_name}输入测试...")
        test_content = f"🧪 {mode_name}测试内容"
        
        input_success = await simple_input_test(driver_wrapper, test_content)
        result['input_success'] = input_success
        
        if input_success:
            print(f"✅ {mode_name}输入成功")
            
            # 按钮测试
            print(f"\n🔍 {mode_name}按钮状态检查...")
            button_ready = await check_button_state(driver_wrapper, posting_executor)
            result['button_ready'] = button_ready
            
            if button_ready:
                print(f"🎉 {mode_name}按钮已激活！")
            else:
                print(f"😞 {mode_name}按钮未激活")
        else:
            print(f"❌ {mode_name}输入失败")
        
        # 关闭浏览器
        browser_pool.sync_close_all(is_final_shutdown=True)
        
    except Exception as e:
        print(f"❌ {mode_name}测试异常: {e}")
        try:
            browser_pool.sync_close_all(is_final_shutdown=True)
        except:
            pass
    
    return result


async def simple_input_test(driver_wrapper, content: str) -> bool:
    """简单输入测试"""
    try:
        # 查找文本框
        selectors = [
            'div[data-testid="tweetTextarea_0"]',
            '[data-testid="tweetTextarea_0"]',
            'div[contenteditable="true"]'
        ]
        
        textbox = None
        for selector in selectors:
            try:
                elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    textbox = elements[0]
                    break
            except:
                continue
        
        if not textbox:
            return False
        
        # 输入内容
        await driver_wrapper.execute_async(lambda: textbox.click())
        await asyncio.sleep(0.5)
        
        from selenium.webdriver.common.keys import Keys
        await driver_wrapper.execute_async(lambda: textbox.send_keys(Keys.CONTROL + 'a'))
        await asyncio.sleep(0.2)
        await driver_wrapper.execute_async(lambda: textbox.send_keys(Keys.DELETE))
        await asyncio.sleep(0.3)
        await driver_wrapper.execute_async(lambda: textbox.send_keys(content))
        await asyncio.sleep(1)
        
        # 验证
        current_text = await get_textbox_content(driver_wrapper)
        return content.strip() in current_text
        
    except Exception as e:
        print(f"输入测试异常: {e}")
        return False


async def get_textbox_content(driver_wrapper) -> str:
    """获取文本框内容"""
    try:
        selectors = [
            'div[data-testid="tweetTextarea_0"]',
            '[data-testid="tweetTextarea_0"]',
            'div[contenteditable="true"]'
        ]
        
        for selector in selectors:
            try:
                elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    textbox = elements[0]
                    text_content = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
                        const element = arguments[0];
                        return element.textContent || element.innerText || '';
                    """, textbox))
                    return text_content.strip()
            except:
                continue
        
        return ""
        
    except Exception as e:
        return ""


async def check_button_state(driver_wrapper, posting_executor) -> bool:
    """检查按钮状态"""
    try:
        tweet_button = await posting_executor._find_element_elegantly(driver_wrapper, 'tweet_button')
        if not tweet_button:
            return False
        
        is_ready = await posting_executor._check_button_ready_headless(driver_wrapper, tweet_button)
        return is_ready
        
    except Exception as e:
        print(f"检查按钮状态异常: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 无头模式 vs 有头模式对比测试")
    print("="*50)
    
    await compare_headless_vs_headed()
    
    print("\n🎉 对比测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
