#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同输入策略的效果
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.core.multi_layer_login_manager import MultiLayerLoginManager
from src.core.account_manager import AccountManager
from src.database.models import Account
from selenium.webdriver.common.by import By


async def test_input_strategies():
    """测试不同的输入策略"""
    print("⌨️ 测试不同输入策略")
    print("="*50)
    
    # 初始化数据库
    try:
        print("📊 初始化数据库...")
        settings = get_settings()
        from src.database.connection import init_db_manager
        init_db_manager(settings.database_url)
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return
    
    # 获取真实账号
    try:
        print("🔍 获取账号13...")
        account_manager = AccountManager()
        account = account_manager.get_account_by_id(13)
        
        if not account:
            print("❌ 未找到账号13，尝试获取其他账号...")
            accounts, total = account_manager.get_accounts()
            if accounts:
                account = accounts[0]
                print(f"✅ 使用账号: {account.username} (ID: {account.id})")
            else:
                print("❌ 没有找到任何账号")
                return
        else:
            print(f"✅ 找到账号13: {account.username}")
            
    except Exception as e:
        print(f"❌ 获取账号失败: {e}")
        return
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    login_manager = MultiLayerLoginManager()
    
    try:
        # 设置无头模式
        original_headless = settings.browser_headless
        settings.browser_headless = True
        
        print(f"\n📱 创建无头模式浏览器...")
        driver_wrapper = await browser_pool.get_driver(account)
        if not driver_wrapper:
            print("❌ 无法创建浏览器")
            return
            
        print("✅ 浏览器创建成功")
        
        # 步骤1: 登录
        print("\n🔑 步骤1: 执行登录...")
        await driver_wrapper.get("https://x.com")
        await asyncio.sleep(3)
        
        login_result = await login_manager.login_account(driver_wrapper, account)
        if not login_result['success']:
            print(f"❌ 登录失败: {login_result['message']}")
            return
        
        print("✅ 登录成功")
        await asyncio.sleep(3)
        
        # 步骤2: 访问compose页面
        print("\n📝 步骤2: 访问compose页面...")
        await driver_wrapper.get("https://x.com/compose/post")
        await asyncio.sleep(5)
        
        # 步骤3: 查找文本框
        print("\n🔍 步骤3: 查找文本框...")
        textbox = await find_textbox(driver_wrapper)
        if not textbox:
            print("❌ 未找到文本框")
            return
        
        print("✅ 找到文本框")
        
        # 步骤4: 测试不同的输入策略
        print("\n⌨️ 步骤4: 测试不同输入策略...")
        test_content = "⌨️ 测试新的输入策略 - 绕过检测"
        
        strategies = [
            ("send_keys", posting_executor._input_strategy_send_keys),
            ("action_chains", posting_executor._input_strategy_action_chains),
            ("clipboard", posting_executor._input_strategy_clipboard),
            ("char_by_char", posting_executor._input_strategy_char_by_char),
            ("hybrid", posting_executor._input_strategy_hybrid)
        ]
        
        successful_strategies = []
        
        for name, strategy in strategies:
            print(f"\n🎯 测试策略: {name}")
            try:
                success = await strategy(driver_wrapper, textbox, test_content)
                if success:
                    print(f"✅ {name} 策略成功")
                    successful_strategies.append(name)
                    
                    # 测试按钮状态
                    await test_button_state_after_input(driver_wrapper, posting_executor, name)
                else:
                    print(f"❌ {name} 策略失败")
                
                # 等待一下再测试下一个策略
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"❌ {name} 策略异常: {e}")
        
        # 步骤5: 总结结果
        print(f"\n📊 步骤5: 测试结果总结")
        print(f"成功的策略: {successful_strategies}")
        print(f"成功率: {len(successful_strategies)}/{len(strategies)}")
        
        if successful_strategies:
            print("🎉 找到了有效的输入策略！")
        else:
            print("😞 所有输入策略都失败了")
        
        # 恢复设置
        settings.browser_headless = original_headless
        browser_pool.sync_close_all(is_final_shutdown=True)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            browser_pool.sync_close_all(is_final_shutdown=True)
        except:
            pass


async def find_textbox(driver_wrapper):
    """查找文本框"""
    try:
        selectors = [
            'div[data-testid="tweetTextarea_0"]',
            '[data-testid="tweetTextarea_0"]',
            'div[contenteditable="true"]'
        ]
        
        for selector in selectors:
            try:
                elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    return elements[0]
            except Exception as e:
                print(f"选择器 {selector} 失败: {e}")
                continue
        
        return None
        
    except Exception as e:
        print(f"查找文本框失败: {e}")
        return None


async def test_button_state_after_input(driver_wrapper, posting_executor, strategy_name):
    """测试输入后的按钮状态"""
    try:
        print(f"  🔍 检查 {strategy_name} 策略后的按钮状态...")
        
        # 查找发帖按钮
        tweet_button = await posting_executor._find_element_elegantly(driver_wrapper, 'tweet_button')
        if not tweet_button:
            print(f"  ❌ 未找到发帖按钮")
            return
        
        # 检查按钮状态
        is_ready = await posting_executor._check_button_ready_headless(driver_wrapper, tweet_button)
        print(f"  按钮状态: {'✅ 就绪' if is_ready else '❌ 未就绪'}")
        
        if is_ready:
            print(f"  🎉 {strategy_name} 策略成功激活了按钮！")
            return True
        else:
            print(f"  😞 {strategy_name} 策略未能激活按钮")
            return False
        
    except Exception as e:
        print(f"  ❌ 检查按钮状态失败: {e}")
        return False


def show_strategy_summary():
    """显示策略总结"""
    print("\n" + "="*60)
    print("⌨️ 输入策略测试总结")
    print("="*60)
    
    print("\n🎯 测试的输入策略:")
    print("  1️⃣ send_keys - Selenium原生输入")
    print("  2️⃣ action_chains - ActionChains输入")
    print("  3️⃣ clipboard - 剪贴板粘贴")
    print("  4️⃣ char_by_char - 逐字符输入")
    print("  5️⃣ hybrid - 混合策略")
    
    print("\n💡 策略特点:")
    print("  🔹 send_keys: 最直接的方式")
    print("  🔹 action_chains: 模拟键盘操作")
    print("  🔹 clipboard: 模拟复制粘贴")
    print("  🔹 char_by_char: 模拟真实打字")
    print("  🔹 hybrid: 结合多种技术")
    
    print("\n🎯 预期效果:")
    print("  ✅ 找到能激活按钮的输入方式")
    print("  ✅ 绕过X平台的输入检测")
    print("  ✅ 提高发帖成功率")


async def main():
    """主函数"""
    print("🚀 输入策略测试工具")
    print("="*50)
    
    await test_input_strategies()
    
    show_strategy_summary()
    
    print("\n🎉 测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
