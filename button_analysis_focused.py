#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专注于按钮分析的调试脚本
模拟已登录状态，专门分析发帖按钮问题
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.config.settings import get_settings
from src.core.browser_manager import BrowserPool
from src.modules.posting.executor import PostingExecutor
from src.database.models import Account
from selenium.webdriver.common.by import By


async def analyze_button_in_compose_mode():
    """在compose模式下分析按钮"""
    print("🔍 专注按钮分析 - 模拟compose模式")
    print("="*50)
    
    # 创建测试账号
    account = Account(
        id=999,
        username="button_test",
        password="test_password",
        email="<EMAIL>",
        cookies="[]",
        status="not_logged_in"
    )
    
    browser_pool = BrowserPool()
    posting_executor = PostingExecutor()
    
    try:
        # 设置无头模式
        settings = get_settings()
        original_headless = settings.browser_headless
        settings.browser_headless = True
        
        print("📱 创建无头模式浏览器...")
        driver_wrapper = await browser_pool.get_driver(account)
        if not driver_wrapper:
            print("❌ 无法创建浏览器")
            return
            
        print("✅ 浏览器创建成功")
        
        # 直接访问compose页面（绕过登录问题）
        print("\n🌐 直接访问compose页面...")
        await driver_wrapper.get("https://x.com/compose/tweet")
        await asyncio.sleep(5)
        
        # 检查页面状态
        current_url = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.current_url)
        page_title = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.title)
        print(f"当前URL: {current_url}")
        print(f"页面标题: {page_title}")
        
        # 分析页面中的所有按钮
        print("\n📊 分析页面中的所有按钮...")
        await analyze_all_buttons(driver_wrapper)
        
        # 尝试查找发帖按钮
        print("\n🔍 尝试查找发帖按钮...")
        await test_button_finding_strategies(driver_wrapper, posting_executor)
        
        # 如果在登录页面，尝试注入一个测试按钮
        if 'login' in current_url or 'signin' in current_url:
            print("\n🧪 注入测试按钮进行分析...")
            await inject_and_test_button(driver_wrapper, posting_executor)
        
        # 恢复设置
        settings.browser_headless = original_headless
        await browser_pool.close_driver(driver_wrapper)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        browser_pool.sync_close_all(is_final_shutdown=True)


async def analyze_all_buttons(driver_wrapper):
    """分析页面中的所有按钮"""
    try:
        button_analysis = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            // 查找所有可能的按钮元素
            const allButtons = document.querySelectorAll('button');
            const allRoleButtons = document.querySelectorAll('[role="button"]');
            const allTestIds = document.querySelectorAll('[data-testid]');
            
            const buttonInfo = [];
            
            // 分析所有button标签
            allButtons.forEach((btn, index) => {
                const rect = btn.getBoundingClientRect();
                buttonInfo.push({
                    type: 'button',
                    index: index,
                    testId: btn.getAttribute('data-testid') || '',
                    ariaLabel: btn.getAttribute('aria-label') || '',
                    text: btn.textContent.trim().substring(0, 50),
                    disabled: btn.disabled,
                    visible: btn.offsetParent !== null,
                    hasValidSize: rect.width > 0 && rect.height > 0,
                    className: btn.className.substring(0, 100)
                });
            });
            
            // 分析所有role=button元素
            allRoleButtons.forEach((btn, index) => {
                if (btn.tagName !== 'BUTTON') {  // 避免重复
                    const rect = btn.getBoundingClientRect();
                    buttonInfo.push({
                        type: 'role-button',
                        index: index,
                        tagName: btn.tagName,
                        testId: btn.getAttribute('data-testid') || '',
                        ariaLabel: btn.getAttribute('aria-label') || '',
                        text: btn.textContent.trim().substring(0, 50),
                        visible: btn.offsetParent !== null,
                        hasValidSize: rect.width > 0 && rect.height > 0,
                        className: btn.className.substring(0, 100)
                    });
                }
            });
            
            // 查找可能的发帖相关元素
            const tweetRelated = [];
            allTestIds.forEach(el => {
                const testId = el.getAttribute('data-testid');
                if (testId && (testId.includes('tweet') || testId.includes('post') || testId.includes('compose'))) {
                    tweetRelated.push({
                        testId: testId,
                        tagName: el.tagName,
                        text: el.textContent.substring(0, 50),
                        visible: el.offsetParent !== null,
                        ariaLabel: el.getAttribute('aria-label') || ''
                    });
                }
            });
            
            return {
                totalButtons: allButtons.length,
                totalRoleButtons: allRoleButtons.length,
                buttonInfo: buttonInfo,
                tweetRelated: tweetRelated
            };
        """))
        
        print(f"页面按钮总数: {button_analysis['totalButtons']}")
        print(f"role=button元素总数: {button_analysis['totalRoleButtons']}")
        
        # 显示发帖相关元素
        if button_analysis['tweetRelated']:
            print(f"\n🎯 发帖相关元素 ({len(button_analysis['tweetRelated'])}):")
            for item in button_analysis['tweetRelated']:
                status = "✅" if item['visible'] else "❌"
                print(f"  {status} {item['testId']} ({item['tagName']}) - {item['text']}")
        
        # 显示前10个按钮的详细信息
        print(f"\n📋 前10个按钮详情:")
        for i, btn in enumerate(button_analysis['buttonInfo'][:10]):
            status = "✅" if btn['visible'] and btn['hasValidSize'] else "❌"
            print(f"  {status} [{btn['type']}] {btn.get('testId', '')} - {btn['text']}")
            if btn.get('ariaLabel'):
                print(f"      aria-label: {btn['ariaLabel']}")
        
    except Exception as e:
        print(f"❌ 按钮分析失败: {e}")


async def test_button_finding_strategies(driver_wrapper, posting_executor):
    """测试不同的按钮查找策略"""
    try:
        print("🔍 测试按钮查找策略...")
        
        # 策略1: 使用原有方法
        print("\n1️⃣ 测试原有查找方法...")
        try:
            tweet_button = await posting_executor._find_element_elegantly(driver_wrapper, 'tweet_button')
            if tweet_button:
                print("✅ 原有方法找到按钮")
                await analyze_found_button(driver_wrapper, tweet_button, "原有方法")
            else:
                print("❌ 原有方法未找到按钮")
        except Exception as e:
            print(f"❌ 原有方法异常: {e}")
        
        # 策略2: 使用无头模式专用方法
        print("\n2️⃣ 测试无头模式专用查找...")
        try:
            tweet_button = await posting_executor._find_tweet_button_headless(driver_wrapper)
            if tweet_button:
                print("✅ 无头模式方法找到按钮")
                await analyze_found_button(driver_wrapper, tweet_button, "无头模式方法")
            else:
                print("❌ 无头模式方法未找到按钮")
        except Exception as e:
            print(f"❌ 无头模式方法异常: {e}")
        
        # 策略3: 直接CSS选择器测试
        print("\n3️⃣ 测试直接CSS选择器...")
        selectors_to_test = [
            'button[data-testid="tweetButtonInline"]',
            'button[data-testid="tweetButton"]',
            'button[aria-label*="Post"]',
            'button[aria-label*="Tweet"]',
            '[data-testid*="tweet"][data-testid*="Button"]'
        ]
        
        for selector in selectors_to_test:
            try:
                elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"✅ 找到元素: {selector} (数量: {len(elements)})")
                    await analyze_found_button(driver_wrapper, elements[0], f"CSS: {selector}")
                else:
                    print(f"❌ 未找到: {selector}")
            except Exception as e:
                print(f"❌ 选择器异常 {selector}: {e}")
        
    except Exception as e:
        print(f"❌ 按钮查找策略测试失败: {e}")


async def analyze_found_button(driver_wrapper, button, method_name):
    """分析找到的按钮"""
    try:
        button_info = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            const button = arguments[0];
            const rect = button.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(button);
            
            return {
                tagName: button.tagName,
                testId: button.getAttribute('data-testid') || '',
                ariaLabel: button.getAttribute('aria-label') || '',
                text: button.textContent.trim(),
                disabled: button.disabled,
                ariaDisabled: button.getAttribute('aria-disabled'),
                visible: button.offsetParent !== null,
                rect: {
                    width: Math.round(rect.width),
                    height: Math.round(rect.height),
                    top: Math.round(rect.top),
                    left: Math.round(rect.left)
                },
                style: {
                    display: computedStyle.display,
                    visibility: computedStyle.visibility,
                    opacity: computedStyle.opacity,
                    pointerEvents: computedStyle.pointerEvents
                }
            };
        """, button))
        
        print(f"    📊 {method_name} 按钮信息:")
        print(f"      标签: {button_info['tagName']}")
        print(f"      testId: {button_info['testId']}")
        print(f"      aria-label: {button_info['ariaLabel']}")
        print(f"      文本: '{button_info['text']}'")
        print(f"      可用: {not button_info['disabled'] and button_info['ariaDisabled'] != 'true'}")
        print(f"      可见: {button_info['visible']}")
        print(f"      尺寸: {button_info['rect']['width']}x{button_info['rect']['height']}")
        
    except Exception as e:
        print(f"    ❌ 按钮分析失败: {e}")


async def inject_and_test_button(driver_wrapper, posting_executor):
    """注入测试按钮并测试检测逻辑"""
    try:
        print("🧪 注入测试按钮...")
        
        # 注入一个模拟的发帖按钮
        test_button = await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
            // 创建一个模拟的发帖按钮
            const button = document.createElement('button');
            button.setAttribute('data-testid', 'tweetButtonInline');
            button.setAttribute('aria-label', 'Post tweet');
            button.textContent = 'Post';
            button.style.display = 'block';
            button.style.visibility = 'visible';
            button.style.pointerEvents = 'auto';
            button.style.width = '100px';
            button.style.height = '40px';
            button.style.position = 'fixed';
            button.style.top = '100px';
            button.style.left = '100px';
            button.style.zIndex = '9999';
            button.style.backgroundColor = '#1d9bf0';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.borderRadius = '20px';
            button.disabled = false;
            
            document.body.appendChild(button);
            return button;
        """))
        
        if test_button:
            print("✅ 测试按钮注入成功")
            
            # 测试我们的检测逻辑
            print("\n🧪 测试无头模式检测逻辑...")
            try:
                is_ready = await posting_executor._check_button_ready_headless(driver_wrapper, test_button)
                print(f"检测结果: {'✅ 就绪' if is_ready else '❌ 未就绪'}")
            except Exception as e:
                print(f"❌ 检测逻辑异常: {e}")
            
            # 测试点击逻辑
            print("\n🧪 测试无头模式点击逻辑...")
            try:
                click_success = await posting_executor._click_button_headless(driver_wrapper, test_button)
                print(f"点击结果: {'✅ 成功' if click_success else '❌ 失败'}")
            except Exception as e:
                print(f"❌ 点击逻辑异常: {e}")
        else:
            print("❌ 测试按钮注入失败")
        
    except Exception as e:
        print(f"❌ 注入测试失败: {e}")


async def main():
    """主函数"""
    print("🚀 专注按钮分析调试工具")
    print("="*50)
    
    await analyze_button_in_compose_mode()
    
    print("\n🎉 分析完成！")


if __name__ == "__main__":
    asyncio.run(main())
